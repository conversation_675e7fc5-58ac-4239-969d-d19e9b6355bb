# Haystack Knowledge Graph Construction Pipeline

This pipeline processes enterprise documents to extract entities and relationships and writes them to a Neo4j-based Knowledge Graph.

## 🧱 Components

- **PreProcessor**: Cleans and chunks the input documents.
- **PromptNode (LLM)**: Uses a language model (e.g., GPT-4) to analyze the cleaned chunks.
- **EntityRelationExtractor**: Custom node that extracts structured triples based on a predefined schema.
- **Neo4jGraphWriter**: Writes the extracted entities and relations to a Neo4j graph database.

---

## ⚙️ Pipeline Code

```python
from haystack import Pipeline
from haystack.nodes import PreProcessor, PromptNode

# Custom nodes (you define these)
from your_nodes import EntityRelationExtractor, Neo4jGraphWriter

pipe = Pipeline()

# Step 1: Preprocess the document (cleaning, chunking)
pipe.add_node(component=PreProcessor(), name="Preprocessor", inputs=["File"])

# Step 2: Use an LLM to process each chunk
pipe.add_node(component=PromptNode(model_name="gpt-4"), name="LLM", inputs=["Preprocessor"])

# Step 3: Extract entities and relationships from LLM output
pipe.add_node(
    component=EntityRelationExtractor(schema=my_schema),
    name="Extractor",
    inputs=["LLM"]
)

# Step 4: Write to the Neo4j graph database
pipe.add_node(
    component=Neo4jGraphWriter(uri="bolt://localhost:7687", auth=("neo4j", "password")),
    name="GraphWriter",
    inputs=["Extractor"]
)
