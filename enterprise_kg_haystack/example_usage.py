#!/usr/bin/env python3
"""
Example Usage of Enterprise KG Haystack Implementation

This script demonstrates how to use the enterprise_kg_haystack implementation
to process documents and build knowledge graphs.
"""

import os
import sys

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


def example_text_processing():
    """Example of processing raw text."""
    print("📝 Example: Processing Raw Text")
    print("=" * 50)
    
    # Sample enterprise text
    sample_text = """
    <PERSON> is the Senior Project Manager for the Digital Transformation Initiative at TechCorp. 
    He reports directly to <PERSON>, who is the VP of Engineering. The project involves 
    implementing a new CRM System that will integrate with the existing Customer Database.
    
    The development team includes <PERSON> (Lead Developer) and <PERSON> (UX Designer). 
    They are working closely with the Data Analytics Team to ensure proper integration 
    with the Business Intelligence Platform.
    
    The project has a budget of $2.5M and is scheduled to complete by Q4 2024. 
    Key stakeholders include the Sales Department and Customer Service Team.
    """
    
    try:
        # This would work if Haystack is installed
        from pipeline import KnowledgeGraphPipeline
        
        # Initialize pipeline
        pipeline = KnowledgeGraphPipeline(
            llm_model="gpt-4",
            neo4j_uri="bolt://localhost:7687",
            neo4j_auth=("neo4j", "password")
        )
        
        # Process the text
        results = pipeline.process_text(sample_text, document_id="sample_enterprise_text")
        
        print(f"✅ Processing completed!")
        print(f"📊 Relationships found: {results.get('total_relationships', 0)}")
        print(f"💾 Relationships stored: {results.get('stored_relationships', 0)}")
        
    except ImportError:
        print("⚠️  Haystack not installed. Showing what would be extracted:")
        
        # Demonstrate with the extractor only
        from nodes.entity_relation_extractor import EntityRelationExtractor
        
        extractor = EntityRelationExtractor(use_all_constants=True)
        
        # Simulate LLM response
        simulated_llm_response = '''[
            {
                "subject": "John Smith",
                "predicate": "works_for",
                "object": "TechCorp",
                "subject_type": "Person",
                "object_type": "Company",
                "confidence_score": 0.95
            },
            {
                "subject": "John Smith",
                "predicate": "manages",
                "object": "Digital Transformation Initiative",
                "subject_type": "Person",
                "object_type": "Project",
                "confidence_score": 0.9
            },
            {
                "subject": "John Smith",
                "predicate": "reports_to",
                "object": "Sarah Johnson",
                "subject_type": "Person",
                "object_type": "Person",
                "confidence_score": 0.95
            },
            {
                "subject": "Sarah Johnson",
                "predicate": "holds_position",
                "object": "VP of Engineering",
                "subject_type": "Person",
                "object_type": "Role",
                "confidence_score": 0.9
            },
            {
                "subject": "CRM System",
                "predicate": "integrates_with",
                "object": "Customer Database",
                "subject_type": "System",
                "object_type": "Database",
                "confidence_score": 0.85
            }
        ]'''
        
        relationships = extractor._extract_json_from_response(simulated_llm_response)
        
        print(f"📊 Extracted {len(relationships)} relationships:")
        for rel in relationships:
            print(f"   • {rel['subject']} --[{rel['predicate']}]--> {rel['object']}")
    
    print()


def example_document_processing():
    """Example of processing document files."""
    print("📁 Example: Processing Document Files")
    print("=" * 50)
    
    # Create sample documents
    sample_docs = {
        "project_report.md": """
# Project Atlas - AI Customer Insights Platform

## Project Overview
Project Atlas is a strategic initiative led by Jennifer Walsh (VP of Product) 
to develop an AI-powered customer insights platform. The project team includes:

- **Project Manager**: David Kim
- **Lead Engineer**: Maria Rodriguez  
- **Data Scientist**: Alex Thompson
- **UX Designer**: Sam Chen

## Technology Stack
- **Backend**: Python FastAPI running on AWS ECS
- **Database**: PostgreSQL with Redis caching
- **ML Platform**: TensorFlow serving on Kubernetes
- **Frontend**: React.js with TypeScript

## Timeline
- **Phase 1**: Data pipeline development (Q1 2024)
- **Phase 2**: ML model training (Q2 2024)  
- **Phase 3**: Frontend development (Q3 2024)
- **Phase 4**: Integration and testing (Q4 2024)

## Budget
Total project budget: $3.2M allocated by the Finance Department.
        """,
        
        "team_structure.md": """
# Engineering Team Structure

## Leadership
- **CTO**: Michael Zhang
- **VP Engineering**: Sarah Johnson
- **VP Product**: Jennifer Walsh

## Development Teams

### Backend Team
- **Team Lead**: Maria Rodriguez
- **Senior Developer**: James Wilson
- **Developer**: Lisa Park

### Frontend Team  
- **Team Lead**: Sam Chen
- **Senior Developer**: Kevin Liu
- **Developer**: Emma Davis

### DevOps Team
- **Team Lead**: Alex Thompson
- **Cloud Engineer**: Ryan Murphy
- **Security Engineer**: Nina Patel

## Reporting Structure
All team leads report to Sarah Johnson (VP Engineering).
The VP Engineering reports to Michael Zhang (CTO).
        """
    }
    
    # Create documents directory if it doesn't exist
    docs_dir = "sample_documents"
    if not os.path.exists(docs_dir):
        os.makedirs(docs_dir)
    
    # Write sample documents
    file_paths = []
    for filename, content in sample_docs.items():
        file_path = os.path.join(docs_dir, filename)
        with open(file_path, 'w') as f:
            f.write(content)
        file_paths.append(file_path)
        print(f"📄 Created: {file_path}")
    
    try:
        # This would work if Haystack is installed
        from pipeline import KnowledgeGraphPipeline
        
        pipeline = KnowledgeGraphPipeline()
        results = pipeline.process_documents(file_paths)
        
        print(f"✅ Processed {results.get('processed_files', 0)} files")
        print(f"📊 Total relationships: {results.get('total_relationships', 0)}")
        
    except ImportError:
        print("⚠️  Haystack not installed. Files created for future processing.")
        print(f"💡 Run this when ready: python main.py --documents {docs_dir}")
    
    print()


def example_constants_usage():
    """Example of using the constants and schemas."""
    print("📚 Example: Using Constants and Schemas")
    print("=" * 50)
    
    from constants.entities import get_all_entity_types, get_person_related_types
    from constants.relationships import get_all_relationship_types
    from constants.schemas import EntityRelationship
    
    # Show available entity types
    entity_types = get_all_entity_types()
    person_types = get_person_related_types()
    relationship_types = get_all_relationship_types()
    
    print(f"📊 Available entity types: {len(entity_types)}")
    print(f"👥 Person-related types: {len(person_types)}")
    print(f"🔗 Relationship types: {len(relationship_types)}")
    
    print("\n🏷️  Sample entity types:")
    for entity_type in list(entity_types)[:10]:
        print(f"   • {entity_type}")
    
    print("\n🔗 Sample relationship types:")
    for rel_type in list(relationship_types)[:10]:
        print(f"   • {rel_type}")
    
    # Create sample relationships
    print("\n📋 Sample EntityRelationship objects:")
    
    sample_relationships = [
        EntityRelationship(
            subject="John Smith",
            predicate="works_for",
            object="TechCorp",
            subject_type="Person",
            object_type="Company",
            confidence_score=0.95
        ),
        EntityRelationship(
            subject="AI Project",
            predicate="managed_by",
            object="Sarah Johnson",
            subject_type="Project",
            object_type="Person",
            confidence_score=0.9
        ),
        EntityRelationship(
            subject="CRM System",
            predicate="integrates_with",
            object="Customer Database",
            subject_type="System",
            object_type="Database",
            confidence_score=0.85
        )
    ]
    
    for rel in sample_relationships:
        print(f"   • {rel.subject} --[{rel.predicate}]--> {rel.object} (confidence: {rel.confidence_score})")
    
    print()


def main():
    """Run all examples."""
    print("🚀 Enterprise KG Haystack - Usage Examples")
    print("=" * 60)
    print()
    
    # Run examples
    example_constants_usage()
    example_text_processing()
    example_document_processing()
    
    print("=" * 60)
    print("🎉 Examples completed!")
    print()
    print("💡 To get started:")
    print("   1. Install dependencies: pip install -r requirements.txt")
    print("   2. Configure .env file with your API keys")
    print("   3. Set up Neo4j database")
    print("   4. Run: python main.py --test-pipeline")
    print("   5. Process your documents: python main.py --documents /path/to/docs")
    print("=" * 60)


if __name__ == "__main__":
    main()
