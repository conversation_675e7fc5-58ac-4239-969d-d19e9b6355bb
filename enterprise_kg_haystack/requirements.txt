# Enterprise KG Haystack Requirements
# Install with: pip install -r requirements.txt

# Core Haystack framework
farm-haystack>=1.20.0

# Neo4j database
neo4j>=5.0.0

# Environment management
python-dotenv>=1.0.0

# Document processing
pypdf>=3.0.0  # For PDF support
python-docx>=0.8.11  # For DOCX support

# LLM providers
openai>=1.0.0
anthropic>=0.7.0

# Optional: For Google Gemini
# google-generativeai>=0.3.0

# Development and testing
pytest>=7.0.0
pytest-cov>=4.0.0

# Code formatting
black>=23.0.0
isort>=5.0.0

# Additional dependencies for document processing
beautifulsoup4>=4.12.0  # For HTML parsing
lxml>=4.9.0  # For XML/HTML processing
markdown>=3.4.0  # For Markdown processing

# Optional: For advanced text processing
# spacy>=3.6.0
# transformers>=4.30.0
