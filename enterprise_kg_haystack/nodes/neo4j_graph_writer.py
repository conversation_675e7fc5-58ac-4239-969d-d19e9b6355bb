"""
Neo4jGraphWriter - Custom Haystack Node

This node writes extracted entity relationships to a Neo4j graph database using
the existing Neo4j client from enterprise_kg_minimal.
"""

import logging
from typing import List, Dict, Any, Optional, Tuple

try:
    from haystack.nodes.base import BaseComponent
except ImportError:
    # For newer versions of Haystack
    try:
        from haystack import BaseComponent
    except ImportError:
        # Fallback for compatibility
        class BaseComponent:
            outgoing_edges = 1
            def __init__(self):
                pass

from constants.schemas import EntityRelationship
from storage.neo4j_client import Neo4jClient, Neo4jConnection

logger = logging.getLogger(__name__)


class Neo4jGraphWriter(BaseComponent):
    """
    Custom Haystack node that writes entity relationships to Neo4j.

    This node takes extracted relationships and stores them in a Neo4j graph
    database using the existing enterprise_kg_minimal Neo4j client.
    """

    outgoing_edges = 1

    def __init__(
        self,
        uri: str = "bolt://localhost:7687",
        auth: Tuple[str, str] = ("neo4j", "password"),
        database: str = "neo4j",
        neo4j_client: Optional[Neo4jClient] = None
    ):
        """
        Initialize the Neo4jGraphWriter.

        Args:
            uri: Neo4j connection URI
            auth: Tuple of (username, password) for authentication
            database: Neo4j database name
            neo4j_client: Optional pre-configured Neo4j client
        """
        super().__init__()

        if neo4j_client:
            self.neo4j_client = neo4j_client
        else:
            # Create Neo4j connection and client
            connection = Neo4jConnection(
                uri=uri,
                user=auth[0],
                password=auth[1],
                database=database
            )
            self.neo4j_client = Neo4jClient(connection)

        self.uri = uri
        self.auth = auth
        self.database = database

    def run(self, relationships: List[EntityRelationship], **kwargs) -> Dict[str, Any]:
        """
        Write entity relationships to Neo4j graph database.

        Args:
            relationships: List of EntityRelationship objects to store

        Returns:
            Dictionary containing storage results and statistics
        """
        if not relationships:
            logger.warning("No relationships provided to store")
            return {
                "stored_relationships": 0,
                "errors": [],
                "success": True
            }

        stored_count = 0
        errors = []

        logger.info(f"Storing {len(relationships)} relationships to Neo4j")

        for relationship in relationships:
            try:
                # Store the relationship using the existing Neo4j client
                result = self.neo4j_client.create_entity_relationship(
                    relationship,
                    source_document=getattr(relationship, 'source_file_id', None)
                )

                if result:
                    stored_count += 1
                    logger.debug(f"Stored relationship: {relationship.subject} -> {relationship.predicate} -> {relationship.object}")
                else:
                    errors.append(f"Failed to store relationship: {relationship.subject} -> {relationship.predicate} -> {relationship.object}")

            except Exception as e:
                error_msg = f"Error storing relationship {relationship.subject} -> {relationship.predicate} -> {relationship.object}: {str(e)}"
                logger.error(error_msg)
                errors.append(error_msg)

        success = stored_count > 0 and len(errors) == 0

        logger.info(f"Successfully stored {stored_count}/{len(relationships)} relationships")
        if errors:
            logger.warning(f"Encountered {len(errors)} errors during storage")

        return {
            "stored_relationships": stored_count,
            "total_relationships": len(relationships),
            "errors": errors,
            "success": success,
            "storage_stats": {
                "success_rate": stored_count / len(relationships) if relationships else 0,
                "error_count": len(errors)
            }
        }

    def test_connection(self) -> bool:
        """
        Test the Neo4j connection.

        Returns:
            True if connection is successful, False otherwise
        """
        try:
            # Test connection by running a simple query
            driver = self.neo4j_client._get_driver()
            with driver.session(database=self.database) as session:
                result = session.run("RETURN 1 as test")
                test_value = result.single()["test"]
                return test_value == 1
        except Exception as e:
            logger.error(f"Neo4j connection test failed: {e}")
            return False

    def get_graph_stats(self) -> Dict[str, Any]:
        """
        Get statistics about the current graph.

        Returns:
            Dictionary containing graph statistics
        """
        try:
            driver = self.neo4j_client._get_driver()
            with driver.session(database=self.database) as session:
                # Count nodes
                node_result = session.run("MATCH (n) RETURN count(n) as node_count")
                node_count = node_result.single()["node_count"]

                # Count relationships
                rel_result = session.run("MATCH ()-[r]->() RETURN count(r) as rel_count")
                rel_count = rel_result.single()["rel_count"]

                # Get node labels
                labels_result = session.run("CALL db.labels()")
                labels = [record["label"] for record in labels_result]

                # Get relationship types
                types_result = session.run("CALL db.relationshipTypes()")
                rel_types = [record["relationshipType"] for record in types_result]

                return {
                    "node_count": node_count,
                    "relationship_count": rel_count,
                    "node_labels": labels,
                    "relationship_types": rel_types
                }
        except Exception as e:
            logger.error(f"Error getting graph stats: {e}")
            return {
                "node_count": 0,
                "relationship_count": 0,
                "node_labels": [],
                "relationship_types": [],
                "error": str(e)
            }

    def close(self):
        """Close the Neo4j connection."""
        if self.neo4j_client:
            self.neo4j_client.close()
