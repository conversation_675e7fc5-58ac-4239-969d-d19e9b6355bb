"""
EntityRelationExtractor - Custom Haystack Node

This node extracts entity relationships from LLM-processed text using the
existing prompt generation and schema validation logic from enterprise_kg_minimal.
"""

import json
import logging
from typing import List, Dict, Any, Optional

try:
    from haystack.nodes.base import BaseComponent
    from haystack.schema import Document
except ImportError:
    # For newer versions of Haystack
    try:
        from haystack import BaseComponent, Document
    except ImportError:
        # Fallback for compatibility
        class BaseComponent:
            outgoing_edges = 1
            def __init__(self):
                pass

        class Document:
            def __init__(self, content, meta=None):
                self.content = content
                self.meta = meta or {}

from constants.schemas import EntityRelationship
from prompt_generator import PromptGenerator, create_full_prompt_generator

logger = logging.getLogger(__name__)


class EntityRelationExtractor(BaseComponent):
    """
    Custom Haystack node that extracts entity relationships from processed text.

    This node takes the output from a PromptNode (LLM) and extracts structured
    entity relationships using the existing enterprise_kg_minimal logic.
    """

    outgoing_edges = 1

    def __init__(
        self,
        schema: Optional[Dict[str, Any]] = None,
        prompt_generator: Optional[PromptGenerator] = None,
        focus_entities: Optional[List[str]] = None,
        focus_relationships: Optional[List[str]] = None,
        use_all_constants: bool = True
    ):
        """
        Initialize the EntityRelationExtractor.

        Args:
            schema: Optional schema definition (for compatibility)
            prompt_generator: Custom prompt generator instance
            focus_entities: Specific entity types to focus on
            focus_relationships: Specific relationship types to focus on
            use_all_constants: Whether to use all available constants
        """
        super().__init__()

        # Initialize prompt generator
        if prompt_generator:
            self.prompt_generator = prompt_generator
        else:
            if use_all_constants:
                self.prompt_generator = create_full_prompt_generator()
            else:
                from prompt_generator import PromptGenerator
                self.prompt_generator = PromptGenerator(
                    focus_entities=focus_entities,
                    focus_relationships=focus_relationships,
                    use_all_constants=False
                )

        self.schema = schema

    def run(self, documents: List[Document], **kwargs) -> Dict[str, Any]:
        """
        Extract entity relationships from the processed documents.

        Args:
            documents: List of documents containing LLM-processed text

        Returns:
            Dictionary containing extracted relationships
        """
        all_relationships = []

        for doc in documents:
            try:
                # Extract relationships from the document content
                relationships = self._extract_relationships_from_text(doc.content)

                # Add document metadata to relationships
                for rel in relationships:
                    if hasattr(doc, 'meta') and doc.meta:
                        rel.source_file_id = doc.meta.get('file_path', 'unknown')
                        rel.extraction_timestamp = doc.meta.get('timestamp')

                all_relationships.extend(relationships)

            except Exception as e:
                logger.error(f"Error extracting relationships from document: {e}")
                continue

        logger.info(f"Extracted {len(all_relationships)} relationships from {len(documents)} documents")

        return {
            "relationships": all_relationships,
            "documents": documents  # Pass through for potential further processing
        }

    def _extract_relationships_from_text(self, text: str) -> List[EntityRelationship]:
        """
        Extract entity relationships from text using the existing logic.

        Args:
            text: Text content to extract relationships from

        Returns:
            List of EntityRelationship objects
        """
        try:
            # Parse the LLM response as JSON
            relationships_data = self._extract_json_from_response(text)

            if not relationships_data:
                logger.warning("No valid JSON found in LLM response")
                return []

            # Convert to EntityRelationship objects
            relationships = []
            for rel_data in relationships_data:
                try:
                    relationship = EntityRelationship(
                        subject=rel_data.get("subject", "").strip(),
                        predicate=rel_data.get("predicate", "").strip(),
                        object=rel_data.get("object", "").strip(),
                        subject_type=rel_data.get("subject_type"),
                        object_type=rel_data.get("object_type"),
                        confidence_score=rel_data.get("confidence_score"),
                        context=rel_data.get("context"),
                        source_sentence=rel_data.get("source_sentence")
                    )
                    relationships.append(relationship)
                except Exception as e:
                    logger.warning(f"Failed to create relationship from data {rel_data}: {e}")
                    continue

            return relationships

        except Exception as e:
            logger.error(f"Error extracting relationships from text: {e}")
            return []

    def _extract_json_from_response(self, response: str) -> List[Dict[str, Any]]:
        """
        Extract JSON data from LLM response text.

        This method handles various formats that LLMs might return.
        """
        if not response or not response.strip():
            return []

        # Try to find JSON in the response
        response = response.strip()

        # Look for JSON array or object
        json_start_markers = ['{', '[']
        json_end_markers = ['}', ']']

        for start_marker, end_marker in zip(json_start_markers, json_end_markers):
            start_idx = response.find(start_marker)
            if start_idx != -1:
                # Find the matching end marker
                end_idx = response.rfind(end_marker)
                if end_idx != -1 and end_idx > start_idx:
                    json_str = response[start_idx:end_idx + 1]
                    try:
                        data = json.loads(json_str)
                        # Ensure we return a list
                        if isinstance(data, dict):
                            return [data]
                        elif isinstance(data, list):
                            return data
                    except json.JSONDecodeError:
                        continue

        # If no JSON found, try to parse the entire response
        try:
            data = json.loads(response)
            if isinstance(data, dict):
                return [data]
            elif isinstance(data, list):
                return data
        except json.JSONDecodeError:
            pass

        logger.warning("Could not extract valid JSON from LLM response")
        return []
