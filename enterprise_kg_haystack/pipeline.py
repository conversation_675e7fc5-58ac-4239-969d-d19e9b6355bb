"""
Haystack Knowledge Graph Construction Pipeline

This module implements the main Haystack pipeline for processing enterprise documents
and building a knowledge graph in Neo4j, as defined in plan.md.
"""

import os
import logging
from typing import List, Dict, Any, Optional

try:
    from haystack import Pipeline
    from haystack.nodes import PreProcessor, PromptNode
    from haystack.schema import Document
except ImportError:
    # For newer versions of Haystack or if not installed
    try:
        from haystack import Pipeline, Document
        from haystack.components.preprocessors import DocumentSplitter as PreProcessor
        from haystack.components.generators import OpenAIGenerator as PromptNode
    except ImportError:
        # Fallback - create minimal classes for development
        class Pipeline:
            def __init__(self):
                self.nodes = {}
            def add_node(self, component, name, inputs):
                self.nodes[name] = component
            def get_node(self, name):
                return self.nodes.get(name)
            def run(self, **kwargs):
                return {}

        class PreProcessor:
            def __init__(self, **kwargs):
                pass

        class PromptNode:
            def __init__(self, **kwargs):
                pass

        class Document:
            def __init__(self, content, meta=None):
                self.content = content
                self.meta = meta or {}

from nodes.entity_relation_extractor import EntityRelationExtractor
from nodes.neo4j_graph_writer import Neo4jGraphWriter
from prompt_generator import create_full_prompt_generator

logger = logging.getLogger(__name__)


class KnowledgeGraphPipeline:
    """
    Main pipeline for processing documents and building knowledge graphs.

    This pipeline follows the architecture defined in plan.md:
    1. PreProcessor: Cleans and chunks documents
    2. PromptNode (LLM): Processes chunks with entity-relationship extraction prompts
    3. EntityRelationExtractor: Extracts structured relationships from LLM output
    4. Neo4jGraphWriter: Writes relationships to Neo4j graph database
    """

    def __init__(
        self,
        llm_model: str = "gpt-4",
        llm_api_key: Optional[str] = None,
        neo4j_uri: str = "bolt://localhost:7687",
        neo4j_auth: tuple = ("neo4j", "password"),
        neo4j_database: str = "neo4j",
        chunk_size: int = 1000,
        chunk_overlap: int = 200,
        use_all_constants: bool = True,
        focus_entities: Optional[List[str]] = None,
        focus_relationships: Optional[List[str]] = None
    ):
        """
        Initialize the Knowledge Graph Pipeline.

        Args:
            llm_model: LLM model name (e.g., "gpt-4", "gpt-3.5-turbo")
            llm_api_key: API key for the LLM (if None, uses environment variable)
            neo4j_uri: Neo4j connection URI
            neo4j_auth: Tuple of (username, password) for Neo4j
            neo4j_database: Neo4j database name
            chunk_size: Size of document chunks for processing
            chunk_overlap: Overlap between chunks
            use_all_constants: Whether to use all entity/relationship constants
            focus_entities: Specific entity types to focus on
            focus_relationships: Specific relationship types to focus on
        """
        self.llm_model = llm_model
        self.llm_api_key = llm_api_key or os.getenv("OPENAI_API_KEY")
        self.neo4j_uri = neo4j_uri
        self.neo4j_auth = neo4j_auth
        self.neo4j_database = neo4j_database
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap

        # Initialize prompt generator
        if use_all_constants:
            self.prompt_generator = create_full_prompt_generator()
        else:
            from prompt_generator import PromptGenerator
            self.prompt_generator = PromptGenerator(
                focus_entities=focus_entities,
                focus_relationships=focus_relationships,
                use_all_constants=False
            )

        # Initialize pipeline
        self.pipeline = self._build_pipeline()

    def _build_pipeline(self) -> Pipeline:
        """
        Build the Haystack pipeline with all components.

        Returns:
            Configured Haystack Pipeline
        """
        pipeline = Pipeline()

        # Step 1: PreProcessor - Clean and chunk documents
        preprocessor = PreProcessor(
            clean_empty_lines=True,
            clean_whitespace=True,
            clean_header_footer=True,
            split_by="word",
            split_length=self.chunk_size,
            split_overlap=self.chunk_overlap,
            split_respect_sentence_boundary=True
        )
        pipeline.add_node(component=preprocessor, name="Preprocessor", inputs=["File"])

        # Step 2: PromptNode (LLM) - Process chunks with entity-relationship extraction
        prompt_template = self._create_prompt_template()

        prompt_node = PromptNode(
            model_name_or_path=self.llm_model,
            api_key=self.llm_api_key,
            default_prompt_template=prompt_template,
            max_length=4000,
            model_kwargs={"temperature": 0.1}
        )
        pipeline.add_node(component=prompt_node, name="LLM", inputs=["Preprocessor"])

        # Step 3: EntityRelationExtractor - Extract structured relationships
        extractor = EntityRelationExtractor(
            prompt_generator=self.prompt_generator,
            use_all_constants=True
        )
        pipeline.add_node(component=extractor, name="Extractor", inputs=["LLM"])

        # Step 4: Neo4jGraphWriter - Write to Neo4j graph database
        graph_writer = Neo4jGraphWriter(
            uri=self.neo4j_uri,
            auth=self.neo4j_auth,
            database=self.neo4j_database
        )
        pipeline.add_node(component=graph_writer, name="GraphWriter", inputs=["Extractor"])

        return pipeline

    def _create_prompt_template(self) -> str:
        """
        Create the prompt template for the LLM node.

        Returns:
            Prompt template string
        """
        # Get the base relationship extraction prompt structure
        sample_content = "Sample enterprise document content for template generation."
        base_prompt = self.prompt_generator.generate_relationship_extraction_prompt(sample_content)

        # Replace the sample content with Haystack template variable
        template = base_prompt.replace(sample_content, "{documents}")

        # Add JSON output instruction
        template += """

Please return your response as a valid JSON array following this exact format:
[
    {
        "subject": "string - the source entity (specific name)",
        "predicate": "string - the relationship type",
        "object": "string - the target entity (specific name)",
        "subject_type": "string - entity type of subject",
        "object_type": "string - entity type of object",
        "confidence_score": "float - confidence in this relationship (0.0-1.0)",
        "context": "string - brief context where this was found",
        "source_sentence": "string - the sentence where this relationship was mentioned"
    }
]

Return only the JSON array, no additional text or explanation."""

        return template

    def process_documents(self, file_paths: List[str]) -> Dict[str, Any]:
        """
        Process a list of document files through the pipeline.

        Args:
            file_paths: List of file paths to process

        Returns:
            Dictionary containing processing results
        """
        if not file_paths:
            logger.warning("No file paths provided for processing")
            return {"success": False, "error": "No files provided"}

        logger.info(f"Processing {len(file_paths)} documents through pipeline")

        try:
            # Run the pipeline
            result = self.pipeline.run(file_paths=file_paths)

            # Extract results from the final node
            if "GraphWriter" in result:
                graph_results = result["GraphWriter"]
                return {
                    "success": True,
                    "processed_files": len(file_paths),
                    "stored_relationships": graph_results.get("stored_relationships", 0),
                    "total_relationships": graph_results.get("total_relationships", 0),
                    "errors": graph_results.get("errors", []),
                    "storage_stats": graph_results.get("storage_stats", {})
                }
            else:
                logger.error("GraphWriter results not found in pipeline output")
                return {"success": False, "error": "Pipeline execution incomplete"}

        except Exception as e:
            logger.error(f"Error processing documents: {e}")
            return {"success": False, "error": str(e)}

    def process_text(self, text: str, document_id: str = "text_input") -> Dict[str, Any]:
        """
        Process raw text through the pipeline.

        Args:
            text: Text content to process
            document_id: Identifier for the text document

        Returns:
            Dictionary containing processing results
        """
        # Create a document object
        doc = Document(content=text, meta={"name": document_id})

        try:
            # Run the pipeline with the document
            result = self.pipeline.run(documents=[doc])

            # Extract results
            if "GraphWriter" in result:
                graph_results = result["GraphWriter"]
                return {
                    "success": True,
                    "document_id": document_id,
                    "stored_relationships": graph_results.get("stored_relationships", 0),
                    "total_relationships": graph_results.get("total_relationships", 0),
                    "errors": graph_results.get("errors", []),
                    "storage_stats": graph_results.get("storage_stats", {})
                }
            else:
                logger.error("GraphWriter results not found in pipeline output")
                return {"success": False, "error": "Pipeline execution incomplete"}

        except Exception as e:
            logger.error(f"Error processing text: {e}")
            return {"success": False, "error": str(e)}

    def test_pipeline(self) -> Dict[str, Any]:
        """
        Test the pipeline components.

        Returns:
            Dictionary containing test results
        """
        test_results = {}

        # Test Neo4j connection
        graph_writer = self.pipeline.get_node("GraphWriter")
        test_results["neo4j_connection"] = graph_writer.test_connection()

        # Test LLM connection (basic check)
        try:
            llm_node = self.pipeline.get_node("LLM")
            test_results["llm_available"] = llm_node is not None
        except Exception as e:
            test_results["llm_available"] = False
            test_results["llm_error"] = str(e)

        # Get graph statistics
        test_results["graph_stats"] = graph_writer.get_graph_stats()

        return test_results
