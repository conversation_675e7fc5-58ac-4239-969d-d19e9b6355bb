#!/usr/bin/env python3
"""
Enterprise Knowledge Graph - Haystack Implementation

Main script for processing enterprise documents using Haystack pipeline
and building a knowledge graph in Neo4j.

This implementation follows the architecture defined in plan.md:
- PreProcessor: Cleans and chunks documents
- PromptNode (LLM): Processes chunks with entity-relationship extraction
- EntityRelationExtractor: Extracts structured relationships
- Neo4jGraphWriter: Writes to Neo4j graph database

Usage:
    python main.py --documents /path/to/docs
    python main.py --text "Sample text to process"
    python main.py --test-pipeline
"""

import os
import sys
import argparse
import logging
from typing import List
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from pipeline import KnowledgeGraphPipeline

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Enterprise Knowledge Graph - Haystack Implementation",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    # Process documents in a directory
    python main.py --documents /path/to/docs

    # Process specific files
    python main.py --files doc1.txt doc2.pdf doc3.md

    # Process raw text
    python main.py --text "John Smith works for TechCorp on the AI Project."

    # Test the pipeline
    python main.py --test-pipeline

    # Use different LLM model
    python main.py --documents /path/to/docs --llm-model gpt-3.5-turbo

    # Custom Neo4j connection
    python main.py --documents /path/to/docs --neo4j-uri bolt://your-neo4j.com:7687
        """
    )

    # Input options
    parser.add_argument(
        "--documents", "-d",
        help="Path to documents directory"
    )
    
    parser.add_argument(
        "--files", "-f",
        nargs="+",
        help="Specific files to process"
    )
    
    parser.add_argument(
        "--text", "-t",
        help="Raw text to process"
    )
    
    parser.add_argument(
        "--test-pipeline",
        action="store_true",
        help="Test the pipeline components"
    )

    # LLM options
    parser.add_argument(
        "--llm-model",
        default=os.getenv("LLM_MODEL", "gpt-4"),
        help="LLM model name (default: gpt-4)"
    )
    
    parser.add_argument(
        "--llm-api-key",
        default=os.getenv("OPENAI_API_KEY"),
        help="LLM API key (default: from OPENAI_API_KEY env var)"
    )

    # Neo4j options
    parser.add_argument(
        "--neo4j-uri",
        default=os.getenv("NEO4J_URI", "bolt://localhost:7687"),
        help="Neo4j URI (default: bolt://localhost:7687)"
    )
    
    parser.add_argument(
        "--neo4j-user",
        default=os.getenv("NEO4J_USER", "neo4j"),
        help="Neo4j username (default: neo4j)"
    )
    
    parser.add_argument(
        "--neo4j-password",
        default=os.getenv("NEO4J_PASSWORD", "password"),
        help="Neo4j password (default: password)"
    )
    
    parser.add_argument(
        "--neo4j-database",
        default=os.getenv("NEO4J_DATABASE", "neo4j"),
        help="Neo4j database name (default: neo4j)"
    )

    # Processing options
    parser.add_argument(
        "--chunk-size",
        type=int,
        default=1000,
        help="Document chunk size (default: 1000)"
    )
    
    parser.add_argument(
        "--chunk-overlap",
        type=int,
        default=200,
        help="Chunk overlap size (default: 200)"
    )
    
    parser.add_argument(
        "--file-patterns",
        nargs="+",
        default=[".md", ".txt", ".docx", ".pdf"],
        help="File patterns to process (default: .md .txt .docx .pdf)"
    )

    # Output options
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Enable verbose logging"
    )

    return parser.parse_args()


def collect_files(documents_path: str, file_patterns: List[str]) -> List[str]:
    """
    Collect files from documents directory based on patterns.
    
    Args:
        documents_path: Path to documents directory
        file_patterns: List of file extensions to include
        
    Returns:
        List of file paths
    """
    if not os.path.exists(documents_path):
        logger.error(f"Documents path does not exist: {documents_path}")
        return []
    
    files = []
    for root, dirs, filenames in os.walk(documents_path):
        for filename in filenames:
            if any(filename.lower().endswith(pattern.lower()) for pattern in file_patterns):
                files.append(os.path.join(root, filename))
    
    return files


def print_results(results: dict):
    """Print processing results in a formatted way."""
    print("\n" + "="*60)
    print("📊 PROCESSING RESULTS")
    print("="*60)
    
    if results.get("success"):
        print(f"✅ Status: SUCCESS")
        if "processed_files" in results:
            print(f"📁 Processed Files: {results['processed_files']}")
        if "document_id" in results:
            print(f"📄 Document ID: {results['document_id']}")
        print(f"🔗 Total Relationships Found: {results.get('total_relationships', 0)}")
        print(f"💾 Relationships Stored: {results.get('stored_relationships', 0)}")
        
        storage_stats = results.get('storage_stats', {})
        if storage_stats:
            success_rate = storage_stats.get('success_rate', 0) * 100
            print(f"📈 Success Rate: {success_rate:.1f}%")
        
        errors = results.get('errors', [])
        if errors:
            print(f"⚠️  Errors: {len(errors)}")
            for error in errors[:3]:  # Show first 3 errors
                print(f"   - {error}")
            if len(errors) > 3:
                print(f"   ... and {len(errors) - 3} more errors")
    else:
        print(f"❌ Status: FAILED")
        print(f"💥 Error: {results.get('error', 'Unknown error')}")
    
    print("="*60)


def main():
    """Main function."""
    args = parse_arguments()
    
    # Set logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    print("🚀 Enterprise Knowledge Graph - Haystack Implementation")
    print("="*60)
    
    # Validate inputs
    if not any([args.documents, args.files, args.text, args.test_pipeline]):
        print("❌ Error: Must specify --documents, --files, --text, or --test-pipeline")
        return 1
    
    # Initialize pipeline
    print("🔧 Initializing Haystack pipeline...")
    try:
        pipeline = KnowledgeGraphPipeline(
            llm_model=args.llm_model,
            llm_api_key=args.llm_api_key,
            neo4j_uri=args.neo4j_uri,
            neo4j_auth=(args.neo4j_user, args.neo4j_password),
            neo4j_database=args.neo4j_database,
            chunk_size=args.chunk_size,
            chunk_overlap=args.chunk_overlap
        )
        print("✅ Pipeline initialized successfully")
    except Exception as e:
        print(f"❌ Failed to initialize pipeline: {e}")
        return 1
    
    # Test pipeline if requested
    if args.test_pipeline:
        print("\n🧪 Testing pipeline components...")
        test_results = pipeline.test_pipeline()
        
        print(f"Neo4j Connection: {'✅' if test_results.get('neo4j_connection') else '❌'}")
        print(f"LLM Available: {'✅' if test_results.get('llm_available') else '❌'}")
        
        graph_stats = test_results.get('graph_stats', {})
        if graph_stats and 'error' not in graph_stats:
            print(f"Graph Nodes: {graph_stats.get('node_count', 0)}")
            print(f"Graph Relationships: {graph_stats.get('relationship_count', 0)}")
        
        return 0
    
    # Process documents
    start_time = datetime.now()
    
    if args.text:
        print(f"\n📝 Processing text input...")
        results = pipeline.process_text(args.text)
        
    elif args.files:
        print(f"\n📁 Processing {len(args.files)} specified files...")
        results = pipeline.process_documents(args.files)
        
    elif args.documents:
        print(f"\n📂 Collecting files from: {args.documents}")
        files = collect_files(args.documents, args.file_patterns)
        
        if not files:
            print("❌ No files found to process")
            return 1
        
        print(f"📁 Found {len(files)} files to process")
        results = pipeline.process_documents(files)
    
    end_time = datetime.now()
    processing_time = (end_time - start_time).total_seconds()
    
    # Print results
    print_results(results)
    print(f"⏱️  Processing Time: {processing_time:.2f} seconds")
    
    # Suggest next steps
    if results.get("success") and results.get("stored_relationships", 0) > 0:
        print("\n💡 Next Steps:")
        print("   1. Open Neo4j Browser to explore your knowledge graph")
        print("   2. Try these sample queries:")
        print("      MATCH (n)-[r]->(m) RETURN n.name, type(r), m.name LIMIT 10")
        print("      MATCH (p:Person)-[:works_for]->(c:Company) RETURN p.name, c.name")
        print("      MATCH (proj:Project)-[:involves]-(entity) RETURN proj.name, entity.name")
    
    return 0 if results.get("success") else 1


if __name__ == "__main__":
    sys.exit(main())
