#!/usr/bin/env python3
"""
Test script for Enterprise KG Haystack implementation

This script tests the core functionality without requiring Haystack to be installed.
It validates that all components can be imported and basic functionality works.
"""

import os
import sys
import logging

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_imports():
    """Test that all modules can be imported."""
    print("🧪 Testing Module Imports")
    print("=" * 40)
    
    modules_to_test = [
        ("constants.entities", "Entity Constants"),
        ("constants.relationships", "Relationship Constants"),
        ("constants.schemas", "Schema Definitions"),
        ("storage.neo4j_client", "Neo4j Client"),
        ("prompt_generator", "Prompt Generator"),
        ("nodes.entity_relation_extractor", "Entity Relation Extractor"),
        ("nodes.neo4j_graph_writer", "Neo4j Graph Writer"),
    ]
    
    all_good = True
    
    for module_name, description in modules_to_test:
        try:
            __import__(module_name)
            print(f"✓ {description}")
        except ImportError as e:
            print(f"✗ {description}: {e}")
            all_good = False
    
    print()
    return all_good


def test_constants():
    """Test that constants are properly loaded."""
    print("📊 Testing Constants")
    print("=" * 40)
    
    try:
        from constants.entities import get_all_entity_types, get_person_related_types
        from constants.relationships import get_all_relationship_types
        
        entity_types = get_all_entity_types()
        person_types = get_person_related_types()
        relationship_types = get_all_relationship_types()
        
        print(f"✓ Entity types loaded: {len(entity_types)}")
        print(f"✓ Person-related types: {len(person_types)}")
        print(f"✓ Relationship types loaded: {len(relationship_types)}")
        
        # Show some examples
        print(f"  Example entities: {list(entity_types)[:5]}")
        print(f"  Example relationships: {list(relationship_types)[:5]}")
        
        print()
        return True
        
    except Exception as e:
        print(f"✗ Constants test failed: {e}")
        print()
        return False


def test_prompt_generator():
    """Test prompt generation functionality."""
    print("📝 Testing Prompt Generator")
    print("=" * 40)
    
    try:
        from prompt_generator import create_full_prompt_generator
        
        generator = create_full_prompt_generator()
        
        # Test relationship extraction prompt
        sample_text = "John Smith works for TechCorp on the AI Project."
        prompt = generator.generate_relationship_extraction_prompt(sample_text)
        
        print(f"✓ Prompt generator created")
        print(f"✓ Generated prompt length: {len(prompt)} characters")
        print(f"✓ Contains sample text: {'John Smith' in prompt}")
        
        # Test schema description
        schema = generator.get_schema_description()
        print(f"✓ Schema description generated: {len(schema)} characters")
        
        print()
        return True
        
    except Exception as e:
        print(f"✗ Prompt generator test failed: {e}")
        print()
        return False


def test_entity_relation_extractor():
    """Test the EntityRelationExtractor node."""
    print("🔍 Testing Entity Relation Extractor")
    print("=" * 40)
    
    try:
        from nodes.entity_relation_extractor import EntityRelationExtractor
        
        # Create extractor
        extractor = EntityRelationExtractor(use_all_constants=True)
        
        print(f"✓ EntityRelationExtractor created")
        print(f"✓ Prompt generator available: {extractor.prompt_generator is not None}")
        
        # Test JSON extraction
        sample_json = '''[
            {
                "subject": "John Smith",
                "predicate": "works_for",
                "object": "TechCorp",
                "subject_type": "Person",
                "object_type": "Company",
                "confidence_score": 0.9
            }
        ]'''
        
        relationships = extractor._extract_json_from_response(sample_json)
        print(f"✓ JSON extraction works: {len(relationships)} relationships found")
        
        print()
        return True
        
    except Exception as e:
        print(f"✗ EntityRelationExtractor test failed: {e}")
        print()
        return False


def test_neo4j_graph_writer():
    """Test the Neo4jGraphWriter node (without actual Neo4j connection)."""
    print("📊 Testing Neo4j Graph Writer")
    print("=" * 40)
    
    try:
        from nodes.neo4j_graph_writer import Neo4jGraphWriter
        from constants.schemas import EntityRelationship
        
        # Create writer (will fail to connect, but that's expected)
        writer = Neo4jGraphWriter(
            uri="bolt://localhost:7687",
            auth=("neo4j", "test_password")
        )
        
        print(f"✓ Neo4jGraphWriter created")
        print(f"✓ Neo4j client available: {writer.neo4j_client is not None}")
        
        # Test with sample relationship
        sample_rel = EntityRelationship(
            subject="John Smith",
            predicate="works_for",
            object="TechCorp",
            subject_type="Person",
            object_type="Company"
        )
        
        print(f"✓ Sample relationship created: {sample_rel.subject} -> {sample_rel.predicate} -> {sample_rel.object}")
        
        print()
        return True
        
    except Exception as e:
        print(f"✗ Neo4jGraphWriter test failed: {e}")
        print()
        return False


def test_schemas():
    """Test schema validation."""
    print("📋 Testing Schemas")
    print("=" * 40)
    
    try:
        from constants.schemas import EntityRelationship, DocumentSummary
        
        # Test EntityRelationship
        rel = EntityRelationship(
            subject="John Smith",
            predicate="works_for",
            object="TechCorp"
        )
        
        print(f"✓ EntityRelationship created: {rel.subject} -> {rel.predicate} -> {rel.object}")
        
        # Test DocumentSummary
        summary = DocumentSummary(
            title="Test Document",
            summary="A test document for validation"
        )
        
        print(f"✓ DocumentSummary created: {summary.title}")
        
        print()
        return True
        
    except Exception as e:
        print(f"✗ Schema test failed: {e}")
        print()
        return False


def main():
    """Run all tests."""
    print("🚀 Enterprise KG Haystack - Implementation Test")
    print("=" * 60)
    print()
    
    tests = [
        test_imports,
        test_constants,
        test_schemas,
        test_prompt_generator,
        test_entity_relation_extractor,
        test_neo4j_graph_writer,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("=" * 60)
    print(f"📊 TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Implementation is ready.")
        print()
        print("💡 Next steps:")
        print("   1. Install Haystack: pip install farm-haystack")
        print("   2. Set up Neo4j and configure credentials")
        print("   3. Run: python main.py --test-pipeline")
        print("   4. Process documents: python main.py --documents /path/to/docs")
    else:
        print("⚠️  Some tests failed. Check the errors above.")
    
    print("=" * 60)
    
    return 0 if passed == total else 1


if __name__ == "__main__":
    sys.exit(main())
