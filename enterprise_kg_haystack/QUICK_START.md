# 🚀 Quick Start Guide - Enterprise KG Haystack

## ⚡ **5-Minute Setup**

### **1. Install Dependencies**
```bash
cd enterprise_kg_haystack
pip install -r requirements.txt
```

### **2. Configure Environment**
```bash
# Copy and edit environment file
cp .env .env.local

# Edit .env.local with your credentials:
# OPENAI_API_KEY=your-openai-key
# NEO4J_PASSWORD=your-neo4j-password
```

### **3. Test the Implementation**
```bash
# Test all components (should show 6/6 tests passed)
python3 test_implementation.py

# Test the pipeline
python3 main.py --test-pipeline
```

### **4. Process Your First Document**
```bash
# Process sample text
python3 main.py --text "<PERSON> works for TechCorp on the AI Project."

# Process sample documents (created by example_usage.py)
python3 main.py --documents sample_documents

# Process your own documents
python3 main.py --documents /path/to/your/documents
```

## 📊 **Expected Results**

After processing, you should see:
```
✅ Status: SUCCESS
📁 Processed Files: 2
🔗 Total Relationships Found: 15
💾 Relationships Stored: 15
📈 Success Rate: 100.0%
```

## 🔍 **Query Your Knowledge Graph**

Open Neo4j Browser and try these queries:

```cypher
-- View all relationships
MATCH (n)-[r]->(m) RETURN n.name, type(r), m.name LIMIT 20

-- Find people and their companies
MATCH (p:Person)-[:works_for]->(c:Company) RETURN p.name, c.name

-- Explore project relationships
MATCH (proj:Project)-[:involves]-(entity) RETURN proj.name, entity.name
```

## 🛠️ **Troubleshooting**

### **Common Issues**

1. **"Haystack not installed"**
   ```bash
   pip install farm-haystack
   ```

2. **"Neo4j connection failed"**
   - Check Neo4j is running: `neo4j status`
   - Verify credentials in `.env`

3. **"OpenAI API error"**
   - Check API key in `.env`
   - Verify account has credits

### **Test Commands**
```bash
# Test without external dependencies
python3 test_implementation.py

# Test with sample data
python3 example_usage.py

# Test pipeline components
python3 main.py --test-pipeline
```

## 🎯 **What's Different from enterprise_kg_minimal?**

| Feature | enterprise_kg_minimal | enterprise_kg_haystack |
|---------|----------------------|----------------------|
| **Architecture** | Monolithic | Modular Haystack pipeline |
| **Usage** | `python main.py --documents /path` | `python main.py --documents /path` |
| **Output** | Same Neo4j graph | Same Neo4j graph |
| **Constants** | ✅ Same entities/relationships | ✅ Same entities/relationships |
| **Extensibility** | Limited | High (Haystack ecosystem) |

## 🚀 **Ready to Scale?**

Once you've tested with sample data:

1. **Process your enterprise documents**:
   ```bash
   python3 main.py --documents /path/to/enterprise/docs --file-patterns .md .txt .docx .pdf
   ```

2. **Use different LLM models**:
   ```bash
   python3 main.py --llm-model gpt-3.5-turbo --documents /path/to/docs
   ```

3. **Connect to cloud Neo4j**:
   ```bash
   python3 main.py --neo4j-uri bolt://your-aura-instance.databases.neo4j.io:7687
   ```

## 🎉 **Success!**

You now have a fully functional Haystack-based knowledge graph pipeline that:
- ✅ Processes enterprise documents
- ✅ Extracts entities and relationships  
- ✅ Stores data in Neo4j
- ✅ Uses the same proven logic as enterprise_kg_minimal
- ✅ Provides modular, extensible architecture

**Happy knowledge graph building!** 🚀
