# Enterprise Knowledge Graph - Haystack Implementation

A Haystack-based implementation for processing enterprise documents and building knowledge graphs in Neo4j. This implementation follows the architecture defined in `plan.md` and reuses the core components from `enterprise_kg_minimal`.

## 🏗️ Architecture

This implementation uses the Haystack framework to orchestrate the knowledge graph construction pipeline:

1. **PreProcessor**: Cleans and chunks input documents
2. **PromptNode (LLM)**: Uses language models to analyze document chunks
3. **EntityRelationExtractor**: Custom node that extracts structured entity relationships
4. **Neo4jGraphWriter**: Custom node that writes relationships to Neo4j

## 🚀 Quick Start

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Set Up Environment

Copy and configure the environment file:

```bash
cp .env.example .env
# Edit .env with your actual credentials
```

Required environment variables:
- `OPENAI_API_KEY` or `ANTHROPIC_API_KEY`: LLM API key
- `NEO4J_PASSWORD`: Your Neo4j password

### 3. Test the Pipeline

```bash
python main.py --test-pipeline
```

### 4. Process Documents

```bash
# Process documents from a directory
python main.py --documents /path/to/your/documents

# Process specific files
python main.py --files doc1.txt doc2.pdf doc3.md

# Process raw text
python main.py --text "John Smith works for TechCorp on the AI Project."
```

## 📁 Project Structure

```
enterprise_kg_haystack/
├── main.py                    # Main entry point
├── pipeline.py                # Haystack pipeline configuration
├── nodes/                     # Custom Haystack nodes
│   ├── __init__.py
│   ├── entity_relation_extractor.py
│   └── neo4j_graph_writer.py
├── constants/                 # Entity and relationship definitions
│   ├── __init__.py
│   ├── entities.py
│   ├── relationships.py
│   └── schemas.py
├── storage/                   # Neo4j client
│   ├── __init__.py
│   └── neo4j_client.py
├── utils/                     # Helper functions
│   ├── __init__.py
│   └── helpers.py
├── prompt_generator.py        # LLM prompt generation
├── requirements.txt           # Dependencies
├── .env                       # Environment configuration
└── README.md                  # This file
```

## 🔧 Configuration

### LLM Models

Supported LLM providers:
- **OpenAI**: `gpt-4`, `gpt-3.5-turbo`
- **Anthropic**: `claude-3-5-sonnet-latest`
- **OpenRouter**: Various models via OpenRouter API

```bash
python main.py --llm-model gpt-3.5-turbo --documents /path/to/docs
```

### Neo4j Configuration

```bash
python main.py --neo4j-uri bolt://your-neo4j.com:7687 --neo4j-user neo4j --neo4j-password your-password
```

### Document Processing

```bash
python main.py --chunk-size 1500 --chunk-overlap 300 --file-patterns .md .txt .docx
```

## 🎯 Key Features

### Reused Components from enterprise_kg_minimal

- **Entity Types**: 88+ predefined entity types (Person, Project, Company, etc.)
- **Relationship Types**: 70+ relationship types (works_for, manages, involved_in, etc.)
- **Prompt Generation**: Dynamic prompt generation based on entity/relationship constants
- **Neo4j Integration**: Full Neo4j client with enhanced properties and GraphRAG optimization
- **Schema Validation**: Robust data validation and error handling

### Haystack Integration

- **Pipeline Architecture**: Clean, modular pipeline using Haystack framework
- **Document Processing**: Automatic chunking and preprocessing
- **LLM Integration**: Seamless integration with multiple LLM providers
- **Custom Nodes**: Purpose-built nodes for entity extraction and graph writing

## 📊 Usage Examples

### Basic Document Processing

```python
from pipeline import KnowledgeGraphPipeline

# Initialize pipeline
kg_pipeline = KnowledgeGraphPipeline(
    llm_model="gpt-4",
    neo4j_uri="bolt://localhost:7687",
    neo4j_auth=("neo4j", "password")
)

# Process documents
results = kg_pipeline.process_documents([
    "project_report.pdf",
    "team_structure.md",
    "system_architecture.docx"
])

print(f"Stored {results['stored_relationships']} relationships")
```

### Text Processing

```python
# Process raw text
text = """
John Smith is the project manager for the AI Initiative at TechCorp. 
He reports to Sarah Johnson, the VP of Engineering. The project uses 
the CRM System and integrates with the Customer Database.
"""

results = kg_pipeline.process_text(text, document_id="sample_text")
```

### Pipeline Testing

```python
# Test all components
test_results = kg_pipeline.test_pipeline()
print(f"Neo4j connected: {test_results['neo4j_connection']}")
print(f"Graph stats: {test_results['graph_stats']}")
```

## 🔍 Querying the Knowledge Graph

After processing documents, explore your knowledge graph in Neo4j Browser:

```cypher
-- View all relationships
MATCH (n)-[r]->(m) RETURN n.name, type(r), m.name LIMIT 20

-- Find people and their companies
MATCH (p:Person)-[:works_for]->(c:Company) RETURN p.name, c.name

-- Explore project relationships
MATCH (proj:Project)-[:involves]-(entity) RETURN proj.name, entity.name

-- Find management hierarchies
MATCH (manager:Person)-[:manages]->(employee:Person) RETURN manager.name, employee.name

-- Technology dependencies
MATCH (sys:System)-[:integrates_with]->(other:System) RETURN sys.name, other.name
```

## 🧪 Testing

```bash
# Test pipeline components
python main.py --test-pipeline

# Process sample text
python main.py --text "Sample enterprise text for testing"

# Verbose output for debugging
python main.py --documents /path/to/docs --verbose
```

## 🔄 Comparison with enterprise_kg_minimal

| Feature | enterprise_kg_minimal | enterprise_kg_haystack |
|---------|----------------------|----------------------|
| **Architecture** | Standalone processor | Haystack pipeline |
| **Document Processing** | Custom file reading | Haystack PreProcessor |
| **LLM Integration** | Direct API calls | Haystack PromptNode |
| **Entity Extraction** | Inline processing | Custom Haystack node |
| **Graph Writing** | Direct Neo4j calls | Custom Haystack node |
| **Modularity** | Monolithic | Modular pipeline |
| **Extensibility** | Limited | High (Haystack ecosystem) |
| **Reusability** | Components coupled | Components decoupled |

## 🚀 Next Steps

1. **Process Your Documents**: Start with a small set of documents to test
2. **Explore the Graph**: Use Neo4j Browser to query and visualize results
3. **Customize Entities**: Modify `constants/entities.py` for your domain
4. **Add Relationships**: Extend `constants/relationships.py` as needed
5. **Scale Up**: Process larger document sets once validated

## 🤝 Contributing

This implementation maintains compatibility with the `enterprise_kg_minimal` constants and schemas, making it easy to switch between implementations or migrate existing data.

## 📝 License

Same license as the parent enterprise_kg project.
